import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch customer balances with pagination and filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const hasBalance = searchParams.get('has_balance') // 'true' to show only customers with remaining balance

  // First get the basic balances
  let query = supabase
    .from('customer_balances')
    .select('*', { count: 'exact' })
    .order('remaining_balance', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%`)
  }

  // Filter customers with remaining balance
  if (hasBalance === 'true') {
    query = query.gt('remaining_balance', 0)
  }

  const { data: balances, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  // Get sukli given records for all customers
  const { data: sukliRecords } = await supabase
    .from('customer_payments')
    .select('customer_name, customer_family_name, notes')
    .like('notes', 'SUKLI_GIVEN:%')

  // Process balances to account for sukli given
  const processedBalances = balances?.map(balance => {
    // Check if sukli has been given for this customer
    const sukliRecord = sukliRecords?.find(record =>
      record.customer_name === balance.customer_name &&
      record.customer_family_name === balance.customer_family_name
    )

    if (sukliRecord) {
      // Extract sukli amount from notes
      const sukliMatch = sukliRecord.notes?.match(/SUKLI_GIVEN:(\d+\.?\d*)/)
      const sukliAmount = sukliMatch ? parseFloat(sukliMatch[1]) : 0

      // If sukli was given, adjust the remaining balance and change amount
      const actualRemaining = balance.total_debt - balance.total_payments
      if (actualRemaining < 0 && sukliAmount > 0) {
        // Sukli was given, so show zero change
        return {
          ...balance,
          remaining_balance: Math.max(actualRemaining + sukliAmount, 0),
          change_amount: 0 // Hide sukli since it was given
        }
      }
    }

    return balance
  }) || []

  return successResponse({
    balances: processedBalances,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})
