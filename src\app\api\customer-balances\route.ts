import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch customer balances with pagination and filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const hasBalance = searchParams.get('has_balance') // 'true' to show only customers with remaining balance

  // First get the basic balances
  let query = supabase
    .from('customer_balances')
    .select('*', { count: 'exact' })
    .order('remaining_balance', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%`)
  }

  // Filter customers with remaining balance
  if (hasBalance === 'true') {
    query = query.gt('remaining_balance', 0)
  }

  const { data: balances, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  // Get sukli given records for all customers
  const { data: sukliRecords } = await supabase
    .from('customer_payments')
    .select('customer_name, customer_family_name, notes, payment_amount')
    .like('notes', 'SUKLI_GIVEN:%')
    .order('created_at', { ascending: false }) // Get most recent sukli records first

  // Process balances to account for sukli given
  const processedBalances = balances?.map(balance => {
    // Check if sukli has been given for this customer (get all records for this customer)
    const customerSukliRecords = sukliRecords?.filter(record =>
      record.customer_name === balance.customer_name &&
      record.customer_family_name === balance.customer_family_name
    ) || []

    if (customerSukliRecords.length > 0) {
      // Calculate total sukli given
      const totalSukliGiven = customerSukliRecords.reduce((total, record) => {
        const sukliMatch = record.notes?.match(/SUKLI_GIVEN:(\d+\.?\d*)/)
        const sukliAmount = sukliMatch ? parseFloat(sukliMatch[1]) : 0
        return total + sukliAmount
      }, 0)

      // If sukli was given, clear the change amount and adjust balance status
      const actualRemaining = balance.total_debt - balance.total_payments
      if (actualRemaining < 0 && totalSukliGiven > 0) {
        // Calculate remaining change after sukli given
        const remainingChange = Math.max(Math.abs(actualRemaining) - totalSukliGiven, 0)

        return {
          ...balance,
          remaining_balance: 0, // No remaining balance since sukli was given
          change_amount: remainingChange, // Show remaining change if any
          balance_status: remainingChange > 0 ? 'Overpaid' : 'Paid' // Update status appropriately
        }
      }
    }

    return balance
  }) || []

  return successResponse({
    balances: processedBalances,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})
